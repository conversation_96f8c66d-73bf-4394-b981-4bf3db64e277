# PM2 集群模式部署指南

## 概述

本文档介绍如何在 Ubuntu 服务器上使用 PM2 集群模式部署 Excel 学习应用，以充分利用服务器的多核性能，同时确保用户会话和状态管理的一致性。

## 当前架构分析

### Session 管理方式
- 使用 NextAuth.js 的 JWT 策略 (`strategy: 'jwt'`)
- Session 数据存储在 JWT token 中，包含用户 ID、用户名、分数、等级等
- JWT token 存储在客户端 cookie 中，无服务器端状态依赖

### 数据存储
- 用户数据存储在 PostgreSQL 数据库中
- 用户进度、分数等通过 API 调用实时从数据库读取和更新
- 无内存中的共享状态

### 架构优势
✅ **天然支持多进程**: JWT 是无状态的，不依赖服务器内存
✅ **数据库集中存储**: 所有持久化数据都在数据库中
✅ **API 无状态设计**: 每个 API 请求都是独立的

## PM2 集群模式配置

### 1. 创建 PM2 配置文件

创建 `ecosystem.config.js` 文件：

```javascript
module.exports = {
  apps: [{
    name: 'excel-learning-app',
    script: 'pnpm',
    args: 'run start',
    instances: 'max', // 或指定具体数量，如 4
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // 性能优化配置
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    
    // 日志配置
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // 自动重启配置
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    
    // 健康检查
    min_uptime: '10s',
    max_restarts: 10,
    
    // 优雅关闭
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 8000
  }]
}
```

### 2. 部署命令

```bash
# 停止当前应用
pm2 stop my-app
pm2 delete my-app

# 使用配置文件启动集群
pm2 start ecosystem.config.js --env production

# 或者直接命令行启动
pm2 start "pnpm run start" --name excel-learning-app -i max --env NODE_ENV=production
```

### 3. 监控和管理

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs excel-learning-app

# 查看监控面板
pm2 monit

# 重启应用
pm2 restart excel-learning-app

# 优雅重载（零停机时间）
pm2 reload excel-learning-app

# 扩缩容
pm2 scale excel-learning-app 8  # 扩展到8个进程
```

## 负载均衡配置

### Nginx 配置示例

```nginx
upstream excel_app {
    least_conn;
    server 127.0.0.1:3000;
    # 如果使用多个端口
    # server 127.0.0.1:3001;
    # server 127.0.0.1:3002;
    # server 127.0.0.1:3003;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://excel_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

## 性能优化建议

### 1. 数据库连接池优化

在 `lib/db.ts` 中优化 Prisma 连接：

```typescript
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
```

### 2. 环境变量配置

确保以下环境变量正确配置：

```bash
# 数据库
DATABASE_URL="postgresql://username:password@localhost:5432/excel_learning"

# NextAuth
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-secret-key"

# 邮件服务
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-email"
SMTP_PASS="your-password"

# 应用配置
NODE_ENV="production"
PORT="3000"
```

## 监控和日志

### 1. PM2 Plus 监控（可选）

```bash
# 注册 PM2 Plus
pm2 plus

# 链接应用到监控平台
pm2 link <secret_key> <public_key>
```

### 2. 日志轮转配置

```bash
# 安装 pm2-logrotate
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

## 故障排除

### 常见问题

1. **内存泄漏**: 设置 `max_memory_restart` 自动重启
2. **端口冲突**: 确保只使用一个端口，让 PM2 处理负载均衡
3. **数据库连接**: 监控数据库连接数，避免连接池耗尽

### 健康检查脚本

创建 `health-check.js`:

```javascript
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/health',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  if (res.statusCode === 200) {
    console.log('Health check passed');
    process.exit(0);
  } else {
    console.log('Health check failed');
    process.exit(1);
  }
});

req.on('error', (err) => {
  console.log('Health check error:', err);
  process.exit(1);
});

req.on('timeout', () => {
  console.log('Health check timeout');
  req.destroy();
  process.exit(1);
});

req.end();
```

## 快速部署

### 使用部署脚本（推荐）

```bash
# 克隆项目后，直接运行部署脚本
./scripts/deploy-cluster.sh

# 或指定实例数量
./scripts/deploy-cluster.sh 4
```

### 手动部署步骤

```bash
# 1. 安装依赖
pnpm install --frozen-lockfile

# 2. 构建应用
pnpm run build

# 3. 运行数据库迁移
pnpm prisma migrate deploy

# 4. 启动集群
pm2 start ecosystem.config.js --env production

# 5. 健康检查
node scripts/health-check.js
```

## Nginx 配置

将 `nginx/excel-learning-app.conf` 复制到 Nginx 配置目录：

```bash
# 复制配置文件
sudo cp nginx/excel-learning-app.conf /etc/nginx/sites-available/

# 创建软链接
sudo ln -s /etc/nginx/sites-available/excel-learning-app.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载 Nginx
sudo systemctl reload nginx
```

## 性能调优建议

### 1. 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p
```

### 2. PM2 监控设置

```bash
# 安装日志轮转
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
pm2 set pm2-logrotate:workerInterval 30

# 设置开机自启
pm2 startup
pm2 save
```

## 监控和维护

### 日常监控命令

```bash
# 查看应用状态
pm2 status

# 查看实时日志
pm2 logs excel-learning-app --lines 100

# 查看监控面板
pm2 monit

# 查看进程详情
pm2 show excel-learning-app

# 重启单个进程
pm2 restart excel-learning-app 0

# 优雅重载所有进程
pm2 reload excel-learning-app
```

### 性能监控

```bash
# 查看内存使用
pm2 monit

# 查看 CPU 使用率
htop

# 查看数据库连接
sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"

# 健康检查
node scripts/health-check.js
```

## 故障排除

### 常见问题及解决方案

1. **应用无法启动**
   ```bash
   # 检查日志
   pm2 logs excel-learning-app --err

   # 检查端口占用
   netstat -tlnp | grep :3000
   ```

2. **内存使用过高**
   ```bash
   # 重启高内存进程
   pm2 restart excel-learning-app

   # 调整内存限制
   pm2 restart excel-learning-app --max-memory-restart 512M
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库状态
   sudo systemctl status postgresql

   # 检查连接数
   sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"
   ```

## 总结

由于本应用采用了 JWT 无状态架构和数据库集中存储的设计，**无需修改任何代码**即可支持 PM2 集群模式。这种架构天然支持水平扩展，可以充分利用服务器的多核性能，同时保证用户会话和状态的一致性。

关键优势：
- ✅ 无状态设计，天然支持集群
- ✅ JWT token 存储在客户端，无服务器依赖
- ✅ 数据库集中存储，数据一致性有保障
- ✅ 零代码修改，直接部署
- ✅ 完整的监控和健康检查机制
- ✅ 自动化部署脚本，一键部署
