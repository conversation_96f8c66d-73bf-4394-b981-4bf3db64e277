'use client'

import { useSession } from 'next-auth/react'
import { log } from '@/app/lib/logger';
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'


interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface SubLevel {
  id: string
  name: string
  description: string
  difficulty: number
  order: number
  points: number
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
  }>
}

interface MainTask {
  id: string
  name: string
  description: string
  difficulty: number
  order: number
  points: number
  isMainTask: boolean
  hasAccess: boolean
  isLocked: boolean
  buttonText?: string
  requiredScore?: number
  children: SubLevel[]
  progress: Array<{
    completed: boolean
    score: number
  }>
}

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [mainTasks, setMainTasks] = useState<MainTask[]>([])
  const [loading, setLoading] = useState(true)
  const [navigatingToLevel, setNavigatingToLevel] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  useEffect(() => {
    if (session) {
      fetchLevels()
    }
  }, [session])

  const fetchLevels = async () => {
    try {
      const response = await fetch('/api/levels')
      if (response.ok) {
        const data = await response.json()
        setMainTasks(data)
      }
    } catch (error) {
      log.error('获取关卡失败:', error)
    } finally {
      setLoading(false)
    }
  }



  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  // 计算所有子任务的完成情况
  const allSubLevels = mainTasks.flatMap(mainTask => mainTask.children)
  const completedLevels = allSubLevels.filter(subLevel => subLevel.progress.length > 0).length
  const totalPoints = allSubLevels.reduce((sum, subLevel) => {
    if (subLevel.progress.length > 0) {
      return sum + subLevel.points
    }
    return sum
  }, 0)

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800'
      case 2:
        return 'bg-yellow-100 text-yellow-800'
      case 3:
        return 'bg-orange-100 text-orange-800'
      case 4:
        return 'bg-red-100 text-red-800'
      case 5:
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return '入门'
      case 2:
        return '简单'
      case 3:
        return '中等'
      case 4:
        return '困难'
      case 5:
        return '专家'
      default:
        return '未知'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50" style={{ paddingTop: '3rem' }}>
      <div className="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
        <div className="px-4 sm:px-0">
          {/* 页面标题 */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
              学习进度
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              选择一个任务开始你的学习之旅，每一步都是成长的足迹
            </p>
          </div>

          {/* 用户进度概览 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
            {/* 已闯关卡片 */}
            <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-14 h-14 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-white text-xl">🏆</span>
                    </div>
                  </div>
                  <div className="ml-4 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 mb-1">
                        已闯关
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {completedLevels}
                      </dd>
                      <dd className="text-sm text-gray-500">
                        / {allSubLevels.length} 关卡
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* 总经验值卡片 */}
            <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-14 h-14 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-white text-xl">⭐</span>
                    </div>
                  </div>
                  <div className="ml-4 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 mb-1">
                        总经验值
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {totalPoints}
                      </dd>
                      <dd className="text-sm text-gray-500">
                        EXP
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* 完成率卡片 */}
            <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-white text-xl">📊</span>
                    </div>
                  </div>
                  <div className="ml-4 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 mb-1">
                        完成率
                      </dt>
                      <dd className="text-2xl font-bold text-gray-900">
                        {allSubLevels.length > 0 ? Math.round((completedLevels / allSubLevels.length) * 100) : 0}%
                      </dd>
                      <dd className="text-sm text-gray-500">
                        学习进度
                      </dd>
                    </dl>
                  </div>
                </div>
                {/* 进度条 */}
                <div className="mt-4">
                  <div className="bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${allSubLevels.length > 0 ? Math.round((completedLevels / allSubLevels.length) * 100) : 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 主任务列表 */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {mainTasks.map((mainTask) => {
              const completedSubLevels = mainTask.children.filter(subLevel =>
                subLevel.progress.length > 0 && subLevel.progress[0].completed
              ).length
              const totalSubLevels = mainTask.children.length
              const progressPercentage = totalSubLevels > 0 ? Math.round((completedSubLevels / totalSubLevels) * 100) : 0
              const isMainTaskCompleted = mainTask.progress.length > 0 && mainTask.progress[0].completed

              // 计算已获积分
              const earnedPoints = mainTask.children
                .filter(subLevel => subLevel.progress.length > 0 && subLevel.progress[0].completed)
                .reduce((sum, subLevel) => sum + subLevel.points, 0)
              
              // 判断关卡状态
              const isFullyCompleted = progressPercentage === 100
              const isPartiallyCompleted = progressPercentage > 0 && progressPercentage < 100
              
              // 获取按钮样式和文字
              const getButtonConfig = () => {
                if (isFullyCompleted) {
                  return {
                    className: "w-full inline-flex justify-center items-center py-3 px-6 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",
                    text: "✅ 已完成",
                    icon: "🏆"
                  }
                } else if (isPartiallyCompleted) {
                  return {
                    className: "w-full inline-flex justify-center items-center py-3 px-6 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",
                    text: "📚 继续学习",
                    icon: "⚡"
                  }
                } else {
                  return {
                    className: "w-full inline-flex justify-center items-center py-3 px-6 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",
                    text: "🚀 开始学习",
                    icon: "🎯"
                  }
                }
              }
              
              const buttonConfig = getButtonConfig()
              
              return (
                <div
                  key={mainTask.id}
                  className={`bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 relative ${
                    isMainTaskCompleted ? 'ring-2 ring-green-200 bg-gradient-to-br from-green-50 to-white' :
                    mainTask.isLocked ? 'opacity-75 cursor-not-allowed' : 'hover:border-gray-200'
                  }`}
                >
                  {/* 锁定/解锁图标 */}
                  <div className="absolute top-4 right-4 z-10">
                    {mainTask.isLocked ? (
                      <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                    ) : mainTask.hasAccess !== undefined && (
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* 卡片顶部装饰条 */}
                  <div className={`h-1 ${
                    isFullyCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                    isPartiallyCompleted ? 'bg-gradient-to-r from-blue-500 to-indigo-500' :
                    mainTask.isLocked ? 'bg-gradient-to-r from-red-400 to-red-500' :
                    'bg-gradient-to-r from-gray-400 to-gray-500'
                  }`}></div>

                  <div className="p-8">
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                          isFullyCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                          isPartiallyCompleted ? 'bg-gradient-to-r from-blue-500 to-indigo-500' :
                          'bg-gradient-to-r from-gray-500 to-gray-600'
                        } shadow-lg`}>
                          <span className="text-white text-xl">
                            {isFullyCompleted ? '🏆' : isPartiallyCompleted ? '📚' : '🎯'}
                          </span>
                        </div>
                        <div>
                          <h2 className="text-xl font-bold text-gray-900 mb-1">
                            {mainTask.name}
                          </h2>
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                            getDifficultyColor(mainTask.difficulty)
                          }`}>
                            {getDifficultyText(mainTask.difficulty)}
                          </span>
                        </div>
                      </div>

                      {isMainTaskCompleted && (
                        <div className="flex items-center space-x-1 text-green-600">
                          <span className="text-lg">✅</span>
                          <span className="text-sm font-medium">完成</span>
                        </div>
                      )}
                    </div>

                    <div className="mb-6 h-12 overflow-hidden">
                      <p className="text-gray-600 leading-6 text-lg" style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}>
                        {mainTask.description}
                      </p>
                    </div>

                    {/* 统计信息网格 */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center p-3 bg-gray-50 rounded-xl">
                        <div className="text-lg font-bold text-gray-900">{completedSubLevels}</div>
                        <div className="text-xs text-gray-500">已完成</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-xl">
                        <div className="text-lg font-bold text-gray-900">{totalSubLevels}</div>
                        <div className="text-xs text-gray-500">总关卡</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-xl">
                        <div className="text-lg font-bold text-blue-600">{earnedPoints}</div>
                        <div className="text-xs text-gray-500">经验值</div>
                      </div>
                    </div>

                    {/* 进度条 */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">学习进度</span>
                        <span className="text-sm font-bold text-gray-900">{progressPercentage}%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
                        <div
                          className={`h-3 rounded-full transition-all duration-500 ${
                            isFullyCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                            isPartiallyCompleted ? 'bg-gradient-to-r from-blue-500 to-indigo-500' :
                            'bg-gradient-to-r from-gray-400 to-gray-500'
                          }`}
                          style={{ width: `${progressPercentage}%` }}
                        ></div>
                      </div>
                    </div>

                    {mainTask.isLocked ? (
                      <div className="w-full px-2 sm:px-6 py-3 bg-gray-300 text-gray-500 rounded-xl text-center font-medium cursor-not-allowed">
                        <span className="flex items-center justify-center space-x-1 sm:space-x-2">
                          <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>
                          <span className="text-xs leading-tight whitespace-nowrap overflow-hidden text-ellipsis font-bold">{mainTask.buttonText || '需要邀请码解锁'}</span>
                        </span>
                      </div>
                    ) : (
                      <button
                        onClick={() => {
                          setNavigatingToLevel(mainTask.id)
                          router.push(`/level/${mainTask.id}`)
                        }}
                        disabled={navigatingToLevel === mainTask.id}
                        className={
                          (mainTask.buttonText && mainTask.buttonText.includes('专属区域，已对您开放')
                            ? "w-full inline-flex justify-center items-center py-3 px-2 sm:px-6 border border-transparent rounded-xl font-semibold text-amber-900 bg-gradient-to-r from-amber-400 to-yellow-400 hover:from-amber-500 hover:to-yellow-500 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                            : buttonConfig.className) + (navigatingToLevel === mainTask.id ? ' opacity-75 cursor-not-allowed' : '')
                        }
                      >
                        <span className="flex items-center justify-center space-x-1 sm:space-x-2">
                          {navigatingToLevel === mainTask.id ? (
                            <>
                              <svg className="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              <span className="text-xs leading-tight whitespace-nowrap overflow-hidden text-ellipsis font-bold">
                                加载中...
                              </span>
                            </>
                          ) : (
                            <>
                              <span className="text-xs leading-tight whitespace-nowrap overflow-hidden text-ellipsis font-bold">{mainTask.buttonText && (mainTask.name === '进阶操作' || mainTask.name === '实用技巧') ? mainTask.buttonText : buttonConfig.text}</span>
                              <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                              </svg>
                            </>
                          )}
                        </span>
                      </button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}