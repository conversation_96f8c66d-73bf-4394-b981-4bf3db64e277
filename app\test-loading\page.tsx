'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function TestLoadingPage() {
  const router = useRouter()
  const [navigatingToLevel, setNavigatingToLevel] = useState<string | null>(null)
  const [navigatingToTask, setNavigatingToTask] = useState<string | null>(null)

  const mockLevels = [
    { id: '1', name: '初识Excel', buttonColor: 'from-blue-500 to-indigo-500' },
    { id: '2', name: '基本公式', buttonColor: 'from-green-500 to-emerald-500' },
    { id: '3', name: '常用操作', buttonColor: 'from-purple-500 to-violet-500' },
  ]

  const mockTasks = [
    { id: '1', name: '创建工作表', buttonColor: 'from-blue-500 to-indigo-500' },
    { id: '2', name: '输入数据', buttonColor: 'from-green-500 to-emerald-500' },
    { id: '3', name: '格式化单元格', buttonColor: 'from-purple-500 to-violet-500' },
  ]

  const handleLevelClick = (levelId: string) => {
    setNavigatingToLevel(levelId)
    // 模拟导航延迟
    setTimeout(() => {
      setNavigatingToLevel(null)
      alert(`导航到关卡 ${levelId}`)
    }, 2000)
  }

  const handleTaskClick = (taskId: string) => {
    setNavigatingToTask(taskId)
    // 模拟导航延迟
    setTimeout(() => {
      setNavigatingToTask(null)
      alert(`导航到任务 ${taskId}`)
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">加载状态测试页面</h1>
        
        {/* Dashboard 风格的卡片 */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-6">Dashboard 风格卡片</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockLevels.map((level) => (
              <div
                key={level.id}
                className="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{level.name}</h3>
                  <p className="text-gray-600 mb-6">这是一个测试关卡的描述信息。</p>
                  
                  <button
                    onClick={() => handleLevelClick(level.id)}
                    disabled={navigatingToLevel === level.id}
                    className={`w-full inline-flex justify-center items-center py-3 px-6 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r ${level.buttonColor} hover:opacity-90 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 ${
                      navigatingToLevel === level.id ? 'opacity-75 cursor-not-allowed' : ''
                    }`}
                  >
                    <span className="flex items-center justify-center space-x-2">
                      {navigatingToLevel === level.id ? (
                        <>
                          <svg className="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>加载中...</span>
                        </>
                      ) : (
                        <>
                          <span>🚀 开始挑战</span>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        </>
                      )}
                    </span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Level 风格的卡片 */}
        <div>
          <h2 className="text-2xl font-semibold mb-6">Level 风格卡片</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockTasks.map((task) => (
              <div
                key={task.id}
                className="bg-white rounded-2xl shadow-xl p-6 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 flex flex-col h-full"
              >
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{task.name}</h3>
                  <p className="text-gray-600 mb-4">这是一个测试任务的描述信息。</p>
                </div>
                
                <div className="mt-auto">
                  <button
                    onClick={() => handleTaskClick(task.id)}
                    disabled={navigatingToTask === task.id}
                    className={`block w-full text-center py-3 px-6 rounded-xl text-sm font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r ${task.buttonColor} text-white hover:opacity-90 ${
                      navigatingToTask === task.id ? 'opacity-75 cursor-not-allowed' : ''
                    }`}
                  >
                    <span className="flex items-center justify-center space-x-2">
                      {navigatingToTask === task.id ? (
                        <>
                          <svg className="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span>加载中...</span>
                        </>
                      ) : (
                        <>
                          <span>🚀 开始挑战</span>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        </>
                      )}
                    </span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="mt-12 text-center">
          <button
            onClick={() => router.back()}
            className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    </div>
  )
}
