# 卡片按钮加载状态功能

## 功能概述

为 Excel 学习应用的 Dashboard 页面和 Level 页面的卡片按钮添加了加载状态，当用户点击按钮时显示旋转的加载圆圈，提升用户体验。

## 实现的功能

### 1. Dashboard 页面卡片按钮加载状态

**文件**: `app/dashboard/page.tsx`

**更改内容**:
- 添加了 `navigatingToLevel` 状态来跟踪正在导航的关卡
- 将 `Link` 组件替换为 `button` 组件，以便控制加载状态
- 在按钮点击时设置加载状态并使用 `router.push()` 进行导航
- 加载时显示白色旋转圆圈和"加载中..."文字
- 加载时按钮变为半透明且不可点击

**加载效果**:
- 🔄 白色旋转圆圈（与登录页面一致）
- 📝 "加载中..." 文字提示
- 🚫 按钮禁用状态（半透明 + 不可点击）

### 2. Level 页面卡片按钮加载状态

**文件**: `app/level/[id]/page.tsx`

**更改内容**:
- 添加了 `navigatingToTask` 状态来跟踪正在导航的任务
- 将 `Link` 组件替换为 `button` 组件
- 实现了与 Dashboard 页面相同的加载效果
- 保持了原有的按钮样式和颜色

**加载效果**:
- 🔄 白色旋转圆圈
- 📝 "加载中..." 文字提示
- 🚫 按钮禁用状态

### 3. 测试页面

**文件**: `app/test-loading/page.tsx`

创建了一个专门的测试页面来演示加载效果：
- 模拟 Dashboard 风格的卡片
- 模拟 Level 风格的卡片
- 2秒延迟来展示加载效果
- 可以访问 `/test-loading` 查看效果

## 技术实现细节

### 加载圆圈 SVG

使用了与登录页面相同的 SVG 动画：

```jsx
<svg className="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
</svg>
```

### 状态管理

```jsx
const [navigatingToLevel, setNavigatingToLevel] = useState<string | null>(null)

const handleClick = (id: string) => {
  setNavigatingToLevel(id)
  router.push(`/level/${id}`)
}
```

### 条件渲染

```jsx
{navigatingToLevel === level.id ? (
  // 加载状态
  <>
    <svg className="animate-spin h-4 w-4 text-white" />
    <span>加载中...</span>
  </>
) : (
  // 正常状态
  <>
    <span>🚀 开始挑战</span>
    <svg className="w-4 h-4" />
  </>
)}
```

## 样式特点

### 颜色适配
- 加载圆圈使用白色 (`text-white`)，适配所有按钮背景色
- 包括蓝色、绿色、紫色、琥珀色等渐变背景

### 动画效果
- 使用 Tailwind CSS 的 `animate-spin` 类
- 平滑的旋转动画
- 与登录页面保持一致的视觉效果

### 禁用状态
- `opacity-75` 半透明效果
- `cursor-not-allowed` 鼠标样式
- `disabled` 属性防止重复点击

## 用户体验改进

1. **视觉反馈**: 用户点击后立即看到加载状态
2. **防止重复点击**: 按钮在加载时被禁用
3. **一致性**: 与登录页面的加载效果保持一致
4. **响应性**: 适配不同屏幕尺寸

## 测试方法

1. 启动开发服务器: `pnpm run dev`
2. 访问测试页面: `http://localhost:3000/test-loading`
3. 点击任意卡片按钮查看加载效果
4. 或者在实际的 Dashboard 和 Level 页面测试

## 兼容性

- ✅ 保持了原有的所有功能
- ✅ 保持了原有的样式和布局
- ✅ 支持所有按钮颜色和状态
- ✅ 响应式设计兼容
- ✅ 无需额外依赖
