#!/bin/bash

# Excel Learning App - PM2 集群部署脚本
# 使用方法: ./scripts/deploy-cluster.sh [instances]
# 例如: ./scripts/deploy-cluster.sh 4

set -e

APP_NAME="excel-learning-app"
INSTANCES=${1:-"max"}

echo "🚀 开始部署 Excel Learning App (集群模式)"
echo "应用名称: $APP_NAME"
echo "实例数量: $INSTANCES"

# 检查 PM2 是否安装
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 未安装，正在安装..."
    npm install -g pm2
fi

# 检查 pnpm 是否安装
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装，正在安装..."
    npm install -g pnpm
fi

# 创建日志目录
mkdir -p logs

# 安装依赖
echo "📦 安装依赖..."
pnpm install --frozen-lockfile

# 构建应用
echo "🔨 构建应用..."
pnpm run build

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
pnpm prisma migrate deploy

# 停止现有应用（如果存在）
echo "🛑 停止现有应用..."
pm2 stop $APP_NAME 2>/dev/null || true
pm2 delete $APP_NAME 2>/dev/null || true

# 启动集群
echo "🚀 启动集群 ($INSTANCES 个实例)..."
if [ "$INSTANCES" = "max" ]; then
    pm2 start ecosystem.config.js --env production
else
    pm2 start ecosystem.config.js --env production -i $INSTANCES
fi

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 10

# 健康检查
echo "🔍 执行健康检查..."
node scripts/health-check.js

# 显示状态
echo "📊 应用状态:"
pm2 status

# 保存 PM2 配置
pm2 save

echo "✅ 部署完成！"
echo ""
echo "常用命令:"
echo "  查看状态: pm2 status"
echo "  查看日志: pm2 logs $APP_NAME"
echo "  监控面板: pm2 monit"
echo "  重启应用: pm2 restart $APP_NAME"
echo "  优雅重载: pm2 reload $APP_NAME"
echo "  扩缩容: pm2 scale $APP_NAME <number>"
echo "  健康检查: node scripts/health-check.js"
