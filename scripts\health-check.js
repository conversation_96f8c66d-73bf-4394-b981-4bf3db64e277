#!/usr/bin/env node

const http = require('http');

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3000,
  path: '/api/health',
  method: 'GET',
  timeout: 5000
};

console.log(`Checking health at http://${options.hostname}:${options.port}${options.path}`);

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    if (res.statusCode === 200) {
      try {
        const healthData = JSON.parse(data);
        console.log('✅ Health check passed');
        console.log(`Status: ${healthData.status}`);
        console.log(`Uptime: ${Math.floor(healthData.uptime)}s`);
        console.log(`PID: ${healthData.pid}`);
        console.log(`Memory: ${Math.round(healthData.memory.rss / 1024 / 1024)}MB`);
        process.exit(0);
      } catch (err) {
        console.log('❌ Invalid health check response');
        process.exit(1);
      }
    } else {
      console.log(`❌ Health check failed with status: ${res.statusCode}`);
      console.log('Response:', data);
      process.exit(1);
    }
  });
});

req.on('error', (err) => {
  console.log('❌ Health check error:', err.message);
  process.exit(1);
});

req.on('timeout', () => {
  console.log('❌ Health check timeout');
  req.destroy();
  process.exit(1);
});

req.setTimeout(options.timeout);
req.end();
